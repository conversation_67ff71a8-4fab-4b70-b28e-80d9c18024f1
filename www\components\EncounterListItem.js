// EncounterListItem.js
/**
 * Erzeugt ein Listenelement für eine Begegnung
 * @param {{ name: string, timestamp: number }} encounter
 * @returns {HTMLLIElement}
 */
export default function createEncounterListItem(encounter) {
    const li = document.createElement('li');
    li.className = 'encounter-item';
    const nameSpan = document.createElement('span');
    nameSpan.className = 'pokemon-name';
    nameSpan.textContent = encounter.name;
    const dateSpan = document.createElement('span');
    dateSpan.className = 'encounter-date';
    const date = new Date(encounter.timestamp);
    dateSpan.textContent = date.toLocaleString('de-DE', {
      day: '2-digit', month: '2-digit', year: 'numeric', hour: '2-digit', minute: '2-digit'
    });
    li.appendChild(nameSpan);
    li.appendChild(dateSpan);
    return li;
  }
  