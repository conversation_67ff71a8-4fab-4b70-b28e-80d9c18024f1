// services/pokemon-spawner.js
// Service for spawning Pokemon in the game world

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import Pokemon from '../Pokemon.js';
import { getChainIndexForLocation } from '../pokemon-grid.js';
import { getLandusePolygonsGeoJSON, getLanduseForLatLng } from '../overpass-landuse.js';
import { LANDUSE_TYPE_MAPPING } from '../landuse-pokemon-types.js';
import { getSpawnLevel as calculateSpawnLevel } from './spawn-levels.js';
import { updatePokemonFromPokedex } from '../utils/pokemon-utils.js';

/**
 * Calculate distance in meters between two lat/lng points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in meters
 */
export function distanceMeters(lat1, lng1, lat2, lng2) {
  function toRad(x) { return x * Math.PI / 180; }
  const R = 6371000;
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Calculate azimuth (direction) between two points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Azimuth in degrees (0-360)
 */
export function calculateAzimuth(lat1, lng1, lat2, lng2) {
  const toRad = deg => deg * Math.PI / 180;
  const toDeg = rad => rad * 180 / Math.PI;
  const dLon = toRad(lng2 - lng1);
  const y = Math.sin(dLon) * Math.cos(toRad(lat2));
  const x = Math.cos(toRad(lat1)) * Math.sin(toRad(lat2)) - Math.sin(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.cos(dLon);
  let brng = Math.atan2(y, x);
  brng = toDeg(brng);
  return (brng + 360) % 360;
}

/**
 * Generate a random offset in a specific direction
 * @param {number} maxMeters - Maximum distance in meters
 * @param {number} baseLat - Base latitude
 * @param {number} baseLng - Base longitude
 * @param {number} lastLat - Last latitude (for directional spawning)
 * @param {number} lastLng - Last longitude (for directional spawning)
 * @param {number} sectorDeg - Sector angle in degrees
 * @returns {Object} - { lat, lng } offset
 */
function randomOffsetInMetersDirectional(maxMeters, baseLat, baseLng, lastLat, lastLng, sectorDeg = 120) {
  // Determine direction
  let angle = Math.random() * 2 * Math.PI; // Default: all directions allowed
  if (typeof lastLat === 'number' && typeof lastLng === 'number' && (baseLat !== lastLat || baseLng !== lastLng)) {
    // Direction: from last spawn point to current position
    const dx = baseLng - lastLng;
    const dy = baseLat - lastLat;
    const dir = Math.atan2(dy, dx); // Direction in radians
    // Sector angle in radians
    const sector = (sectorDeg / 180) * Math.PI;
    const offset = (Math.random() - 0.5) * sector; // e.g. ±60°
    angle = dir + offset;
  }
  const distance = Math.random() * maxMeters;
  // Convert meters to degrees
  const dLat = Math.cos(angle) * (distance / 111320);
  const dLng = Math.sin(angle) * (distance / (111320 * Math.cos(baseLat * Math.PI / 180)));
  return {
    lat: dLat,
    lng: dLng
  };
}

export class PokemonSpawner {
  constructor() {
    this.spawnRadius = config.pokemon.spawnRadius;
    this.spawnDistanceTrigger = config.pokemon.spawnDistanceTrigger;
    this.spawnBatchSize = config.pokemon.spawnBatchSize;
    this.landuseSpecialBatchSize = config.pokemon.landuseSpecialBatchSize;
    this.maxPokemons = config.pokemon.maxPokemons;

    // Rarity probabilities for Pokémon spawning
    this.rarityProbabilities = {
      common: 0.65,    // 65%
      scarce: 0.22,    // 22%
      rare: 0.08,      // 8%
      legendary: 0.04, // 4%
      mythical: 0.01   // 1%
    };
  }

  /**
   * Recreate Pokemon from stored data
   * @param {Array} storedPokemons - Array of stored Pokemon data
   * @returns {Array} - Array of recreated Pokemon objects
   */
  recreateStoredPokemons(storedPokemons) {
    const recreated = [];

    if (!storedPokemons || !Array.isArray(storedPokemons) || storedPokemons.length === 0) {
      logger.debug('No stored Pokemon to recreate');
      return recreated;
    }

    logger.info(`Recreating ${storedPokemons.length} Pokemon from storage`);

    // Ensure pokedex data is loaded
    const pokedexData = gameState.pokedexData;
    if (!pokedexData || pokedexData.length === 0) {
      logger.warn('Pokedex data not loaded, Pokemon may have missing dex_number values');
    }

    for (const storedPokemon of storedPokemons) {
      try {
        // Use Pokemon.fromJSON to properly recreate the Pokemon with all properties
        const pokemon = Pokemon.fromJSON(storedPokemon);

        // Ensure spawn location properties are set
        if (storedPokemon.spawnLat && storedPokemon.spawnLng) {
          pokemon.spawnLat = storedPokemon.spawnLat;
          pokemon.spawnLng = storedPokemon.spawnLng;
          pokemon.lat = storedPokemon.lat || storedPokemon.spawnLat;
          pokemon.lng = storedPokemon.lng || storedPokemon.spawnLng;
        }

        // Ensure landuse properties are set
        pokemon.landuseSpecial = storedPokemon.landuseSpecial || false;
        pokemon.landuseType = storedPokemon.landuseType || null;
        pokemon.landuseTypeName = storedPokemon.landuseTypeName || null;
        pokemon.featureId = storedPokemon.featureId || null;

        // We don't automatically set landuseSpecial based on landuseType
        // Only Pokemon that were originally spawned as special landuse Pokemon should have landuseSpecial=true

        // Update Pokemon with data from pokedex if needed
        if (!pokemon.dex_number || !pokemon.types || !pokemon.types.length || !pokemon.image) {
          const updated = updatePokemonFromPokedex(pokemon, pokedexData);
          if (updated) {
            logger.debug(`Updated Pokemon ${pokemon.name} with data from pokedex during recreation`);
          }
        }

        // If image is still not set but dex_number is available, set image based on dex_number
        if (!pokemon.image && pokemon.dex_number) {
          pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
        }

        // Log successful recreation with dex_number for debugging
        logger.debug(`Recreated Pokemon ${pokemon.name} (ID: ${pokemon.id}) with dex_number: ${pokemon.dex_number}`);

        // Add to game state
        gameState.addPokemon(pokemon);
        recreated.push(pokemon);
      } catch (e) {
        logger.error('Error recreating Pokemon from storage:', e, storedPokemon);
      }
    }

    logger.info(`Successfully recreated ${recreated.length} Pokemon from storage`);
    return recreated;
  }

  /**
   * Collect Pokemon data for spawning (parallel data collection)
   * @param {number} lat - Base latitude
   * @param {number} lng - Base longitude
   * @param {boolean} useDirectionalSpawn - Whether to use directional spawning
   * @param {number|null} movementAzimuth - Direction of movement in degrees
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object|null>} - Collected Pokemon data or null if failed
   */
  async collectPokemonData(lat, lng, useDirectionalSpawn, movementAzimuth, pokedexSnapshot) {
    try {
      // Calculate spawn location
      const spawnLocation = this.calculateSpawnLocation(lat, lng, useDirectionalSpawn, movementAzimuth);

      // Get base Pokemon for this grid location
      const basePokeEntry = this.getBasePokemonForGrid(spawnLocation.lat, spawnLocation.lng);
      if (!basePokeEntry) return null;

      // Get spawn level based on player's team average level
      const level = await this.getSpawnLevel();

      // Calculate evolution data with isolated pokedex snapshot
      const evolutionData = await this.calculateEvolutionData(basePokeEntry.name, level, pokedexSnapshot);

      // Get landuse data parallel
      const landuseData = await this.getLanduseData(spawnLocation.lat, spawnLocation.lng);

      // Return all collected data
      return {
        basePokeEntry,
        level,
        evolutionData,
        spawnLocation,
        landuseData,
        timestamp: Date.now() // For debugging
      };
    } catch (e) {
      logger.error('Error collecting Pokemon data:', e);
      return null;
    }
  }

  /**
   * Calculate spawn location
   * @param {number} lat - Base latitude
   * @param {number} lng - Base longitude
   * @param {boolean} useDirectionalSpawn - Whether to use directional spawning
   * @param {number|null} movementAzimuth - Direction of movement in degrees
   * @returns {Object} - {lat, lng} spawn location
   */
  calculateSpawnLocation(lat, lng, useDirectionalSpawn, movementAzimuth) {
    try {
      if (useDirectionalSpawn && movementAzimuth !== null) {
        // Directional spawn - use turf.js destination
        // Convert sector angle to radians for random offset
        const sectorAngle = config.ui.debugSectorAngle;
        const randomAngleOffset = ((Math.random() - 0.5) * sectorAngle);
        const distance = Math.random() * this.spawnRadius;

        // Calculate destination point in the direction of movement with random offset
        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          movementAzimuth + randomAngleOffset
        );

        return {
          lat: dest.geometry.coordinates[1],
          lng: dest.geometry.coordinates[0]
        };
      } else {
        // Non-directional spawn (360 degrees)
        const randomAngle = Math.random() * 360;
        const distance = Math.random() * this.spawnRadius;

        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          randomAngle
        );

        return {
          lat: dest.geometry.coordinates[1],
          lng: dest.geometry.coordinates[0]
        };
      }
    } catch (e) {
      logger.error('Error using turf.js for spawn location calculation:', e);
      // Fallback to non-directional if turf fails
      const offset = randomOffsetInMetersDirectional(this.spawnRadius, lat, lng, lat, lng, 360);
      return {
        lat: lat + offset.lat,
        lng: lng + offset.lng
      };
    }
  }

  /**
   * Calculate evolution data with isolated pokedex
   * @param {string} baseName - Base Pokemon name
   * @param {number} level - Pokemon level
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object>} - Evolution data {name, sprite, dex_number, types}
   */
  async calculateEvolutionData(baseName, level, pokedexSnapshot) {
    // Create temporary Pokemon object only for evolution calculation
    const tempPokemon = new Pokemon(baseName, 'normal', level);

    // Calculate evolution with isolated pokedex
    const displayForm = await tempPokemon.getDisplayForm(pokedexSnapshot);

    // Validate evolution result
    if (!displayForm || !displayForm.name || !displayForm.dex_number) {
      logger.error(`Invalid evolution result for ${baseName} level ${level}`);

      // Fallback to base Pokemon data
      const baseEntry = pokedexSnapshot.find(p =>
        p.name.toLowerCase() === baseName.toLowerCase()
      );

      if (baseEntry) {
        return {
          name: baseEntry.name,
          sprite: baseEntry.image_url,
          dex_number: baseEntry.dex_number,
          types: baseEntry.types
        };
      } else {
        // Ultimate fallback
        return {
          name: baseName,
          sprite: '',
          dex_number: null,
          types: ['normal']
        };
      }
    }

    return {
      name: displayForm.name,
      sprite: displayForm.sprite,
      dex_number: displayForm.dex_number,
      types: displayForm.types
    };
  }

  /**
   * Get landuse data for location
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @returns {Promise<Object|null>} - Landuse data or null
   */
  async getLanduseData(lat, lng) {
    try {
      const landuseData = await getLanduseForLatLng(lat, lng);
      if (landuseData && landuseData.value) {
        return {
          value: landuseData.value
        };
      }
    } catch (e) {
      logger.error('Error getting landuse data:', e);
    }
    return null;
  }

  /**
   * Create final Pokemon object with pre-calculated data
   * @param {Object} pokemonData - Pre-calculated Pokemon data
   * @returns {Pokemon|null} - Final Pokemon object or null
   */
  createFinalPokemon(pokemonData) {
    try {
      // Create Pokemon with BASE name first, then set evolution data
      const pokemon = new Pokemon(
        pokemonData.basePokeEntry.name,
        pokemonData.basePokeEntry.types[0],
        pokemonData.level
      );

      // Set base properties first
      pokemon.base_name = pokemonData.basePokeEntry.name;
      pokemon.base_sprite = pokemonData.basePokeEntry.image_url;
      pokemon.rarity = pokemonData.basePokeEntry.rarity || 'common';

      // Now set the evolved form data (this is the key fix!)
      pokemon.name = pokemonData.evolutionData.name;
      pokemon.image_url = pokemonData.evolutionData.sprite;
      pokemon.image = pokemonData.evolutionData.sprite; // Ensure image is set
      pokemon.dex_number = pokemonData.evolutionData.dex_number;
      pokemon.types = pokemonData.evolutionData.types;

      // Set evolution chain ID to prevent getDisplayForm from being called again
      if (pokemonData.basePokeEntry.evolution_chain_id) {
        pokemon.evolution_chain_id = pokemonData.basePokeEntry.evolution_chain_id;
      }

      // Set spawn location
      pokemon.spawnLat = pokemonData.spawnLocation.lat;
      pokemon.spawnLng = pokemonData.spawnLocation.lng;
      pokemon.lat = pokemonData.spawnLocation.lat;
      pokemon.lng = pokemonData.spawnLocation.lng;

      // Set landuse data if available
      if (pokemonData.landuseData) {
        pokemon.landuseTypeName = pokemonData.landuseData.value;
        pokemon.landuseType = pokemonData.landuseData.value;
        // For standard spawns, landuseSpecial remains false (default)
      }

      // Final fallback for image if still not set
      if (!pokemon.image && pokemon.dex_number) {
        pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
      }

      logger.debug(`Created final Pokemon: ${pokemon.name} (base: ${pokemon.base_name}) Level ${pokemon.level}, Dex #${pokemon.dex_number}`);

      return pokemon;
    } catch (e) {
      logger.error('Error creating final Pokemon:', e, pokemonData);
      return null;
    }
  }

  /**
   * Spawn random Pokemon around a location (with isolated data collection)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnRandomPokemons(lat, lng, count = this.spawnBatchSize) {
    const { pokemons } = gameState;

    // Remove oldest if exceeding max
    if (pokemons.length + count > this.maxPokemons) {
      const toRemove = (pokemons.length + count) - this.maxPokemons;
      for (let i = 0; i < toRemove; i++) {
        if (pokemons.length > 0) {
          const removed = pokemons.shift();
          if (removed && gameState.pokemonMarkers.has(removed.id)) {
            gameState.map.removeLayer(gameState.pokemonMarkers.get(removed.id).marker);
            gameState.pokemonMarkers.delete(removed.id);
          }
        }
      }
    }

    // 1. Create Pokedex snapshot to prevent race conditions
    const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));
    logger.debug(`Created pokedex snapshot with ${pokedexSnapshot.length} entries`);

    // Determine if we should use directional spawning
    let useDirectionalSpawn = false;
    let movementAzimuth = null;

    // Check if we've moved at least 250m from the last spawn point
    if (gameState.lastSpawnLatLng && (lat !== gameState.lastSpawnLatLng.lat || lng !== gameState.lastSpawnLatLng.lng)) {
      try {
        // Calculate distance from last spawn point
        const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

        // Only use directional spawning if we've moved at least 250m
        if (dist >= config.pokemon.spawnDistanceTrigger) {
          // Calculate bearing from last spawn point to current position
          movementAzimuth = turf.bearing(
            [gameState.lastSpawnLatLng.lng, gameState.lastSpawnLatLng.lat],
            [lng, lat]
          );
          // Normalize to 0-360 degrees
          movementAzimuth = (movementAzimuth + 360) % 360;
          useDirectionalSpawn = true;

          logger.debug(`Using directional spawn with azimuth: ${movementAzimuth}° (moved ${dist}m)`);
        }
      } catch (e) {
        logger.error('Error calculating direction for random Pokemon spawn:', e);
        useDirectionalSpawn = false;
      }
    }

    // 2. Collect all Pokemon data in parallel (time-consuming operations)
    logger.debug(`Starting parallel data collection for ${count} Pokemon...`);
    const pokemonDataPromises = [];

    for (let i = 0; i < count; i++) {
      pokemonDataPromises.push(this.collectPokemonData(lat, lng, useDirectionalSpawn, movementAzimuth, pokedexSnapshot));
    }

    // Wait for all data collection to complete (parallel)
    const pokemonDataResults = await Promise.all(pokemonDataPromises);

    // 3. Create Pokemon objects sequentially with pre-calculated data (fast, no race conditions)
    const finalizedPokemon = [];
    for (const pokemonData of pokemonDataResults) {
      if (pokemonData) {
        const finalPokemon = this.createFinalPokemon(pokemonData);
        if (finalPokemon) {
          finalizedPokemon.push(finalPokemon);
          gameState.addPokemon(finalPokemon);
        }
      }
    }

    logger.debug(`Successfully created ${finalizedPokemon.length} Pokemon with isolated spawning system`);
    return finalizedPokemon;
  }

  /**
   * Get the base Pokemon for a grid cell
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @returns {Object|null} - The base Pokemon or null if not found
   */
  getBasePokemonForGrid(lat, lon) {
    if (!gameState.chainsList.length) return null;
    const chainIdx = getChainIndexForLocation(lat, lon, gameState.chainsList.length);
    const chainId = gameState.chainsList[chainIdx];

    // Get the base Pokemon for this chain
    const basePokemon = gameState.basePokemonByChain[chainId];
    if (!basePokemon) return null;

    logger.debug(`Found base Pokemon for chain ${chainId}: ${basePokemon.name} (${basePokemon.dex_number})`);
    return basePokemon;
  }

  /**
   * Select a rarity based on the defined probabilities
   * @returns {string} - The selected rarity (common, scarce, rare, legendary, mythical)
   */
  selectRarity() {
    const random = Math.random();
    let cumulativeProbability = 0;

    for (const [rarity, probability] of Object.entries(this.rarityProbabilities)) {
      cumulativeProbability += probability;
      if (random <= cumulativeProbability) {
        return rarity;
      }
    }

    // Fallback to common if something goes wrong
    return 'common';
  }

  /**
   * Get a spawn level for a wild Pokemon based on the player's team
   * Uses the spawn-levels.js module to calculate the level
   * @returns {Promise<number>} - The spawn level (minimum 1)
   */
  async getSpawnLevel() {
    try {
      // Use the imported function from spawn-levels.js
      return await calculateSpawnLevel();
    } catch (e) {
      logger.error('Error determining spawn level:', e);
      // Fallback to a random level between 1-10 if there's an error
      const fallbackLevel = Math.floor(Math.random() * 10) + 1;
      logger.debug(`Using fallback spawn level: ${fallbackLevel}`);
      return fallbackLevel;
    }
  }

  /**
   * Spawn special Pokemon based on land use (with isolated data collection)
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnLanduseSpecialPokemons(lat, lng, count = this.landuseSpecialBatchSize) {
    try {
      // 1. Create Pokedex snapshot to prevent race conditions
      const pokedexSnapshot = JSON.parse(JSON.stringify(gameState.pokedexData));
      logger.debug(`Created pokedex snapshot for landuse spawning with ${pokedexSnapshot.length} entries`);

      // Get all landuse/natural/leisure polygons in the area as GeoJSON
      const radius = this.spawnRadius;
      const geojson = await getLandusePolygonsGeoJSON(lat, lng, radius);
      const features = geojson.features.filter(f => f && f.properties && LANDUSE_TYPE_MAPPING[f.properties.value]);

      if (!features.length) {
        logger.debug('No landuse features found for spawning');
        return [];
      }

      // 2. Collect all Pokemon data in parallel (time-consuming operations)
      logger.debug(`Starting parallel landuse data collection for ${count} Pokemon...`);
      const pokemonDataPromises = [];

      for (let i = 0; i < count; i++) {
        pokemonDataPromises.push(this.collectLandusePokemonData(features, pokedexSnapshot));
      }

      // Wait for all data collection to complete (parallel)
      const pokemonDataResults = await Promise.all(pokemonDataPromises);

      // 3. Create Pokemon objects sequentially with pre-calculated data (fast, no race conditions)
      const spawned = [];
      for (const pokemonData of pokemonDataResults) {
        if (pokemonData) {
          const finalPokemon = this.createFinalLandusePokemon(pokemonData);
          if (finalPokemon) {
            spawned.push(finalPokemon);
            gameState.addPokemon(finalPokemon);
          }
        }
      }

      logger.debug(`Successfully created ${spawned.length} landuse Pokemon with isolated spawning system`);
      return spawned;
    } catch (e) {
      logger.error('Error spawning landuse special Pokemon:', e);
      return [];
    }
  }

  /**
   * Collect landuse Pokemon data for spawning (parallel data collection)
   * @param {Array} features - Landuse features
   * @param {Array} pokedexSnapshot - Isolated pokedex data snapshot
   * @returns {Promise<Object|null>} - Collected landuse Pokemon data or null if failed
   */
  async collectLandusePokemonData(features, pokedexSnapshot) {
    try {
      // Helper function: Pick a feature by weight
      function pickFeatureByWeight(features, weights) {
        const r = Math.random();
        let acc = 0;
        for (let i = 0; i < features.length; i++) {
          acc += weights[i];
          if (r <= acc) return features[i];
        }
        return features[features.length - 1];
      }

      // Calculate weights for feature selection (simplified for parallel processing)
      const areas = features.map(f => turf.area(f));
      const alpha = 0.2; // Weighting factor for area
      let weights = areas.map(a => Math.pow(1 / (a || 1), alpha));

      // Normalize weights
      const sum = weights.reduce((a, b) => a + b, 0);
      const normWeights = weights.map(w => w / sum);

      // Pick a polygon by weight
      const feature = pickFeatureByWeight(features, normWeights);
      if (!feature) return null;

      const landuseType = feature.properties.value;
      const mapping = LANDUSE_TYPE_MAPPING[landuseType];
      if (!mapping || !mapping.type) return null;

      // Get all Pokémon of this type from the pokedex
      const allTypeMatches = pokedexSnapshot.filter(
        p => p.types && p.types.includes(mapping.type)
      );
      if (!allTypeMatches || allTypeMatches.length === 0) return null;

      // Select a rarity based on the defined probabilities
      const targetRarity = this.selectRarity();

      // Try to find Pokémon with the selected rarity and matching type
      let candidates = allTypeMatches.filter(p => p.rarity === targetRarity);

      // If no Pokémon with the selected rarity is available, try other rarities
      if (!candidates || candidates.length === 0) {
        // Define fallback order based on the selected rarity
        let fallbackRarities;
        switch (targetRarity) {
          case 'mythical':
            fallbackRarities = ['legendary', 'rare', 'scarce', 'common'];
            break;
          case 'legendary':
            fallbackRarities = ['rare', 'scarce', 'common', 'mythical'];
            break;
          case 'rare':
            fallbackRarities = ['scarce', 'common', 'legendary', 'mythical'];
            break;
          case 'scarce':
            fallbackRarities = ['common', 'rare', 'legendary', 'mythical'];
            break;
          case 'common':
          default:
            fallbackRarities = ['scarce', 'rare', 'legendary', 'mythical'];
            break;
        }

        // Try each fallback rarity in order
        for (const fallbackRarity of fallbackRarities) {
          candidates = allTypeMatches.filter(p => p.rarity === fallbackRarity);
          if (candidates && candidates.length > 0) {
            break;
          }
        }

        // If still no candidates, use all Pokémon of this type regardless of rarity
        if (!candidates || candidates.length === 0) {
          candidates = allTypeMatches;
        }
      }

      if (!candidates || candidates.length === 0) return null;

      // Generate a random point in the polygon
      let testLng, testLat;

      try {
        // First try to generate a random point within the polygon
        const spawnPoint = turf.randomPoint(1, {bbox: turf.bbox(feature)}).features[0];

        // Check if point is actually in the polygon, otherwise use a point on the feature
        if (turf.booleanPointInPolygon(spawnPoint, feature)) {
          [testLng, testLat] = spawnPoint.geometry.coordinates;
        } else {
          // If random point is outside polygon, use a point on the feature
          const pointOnFeature = turf.pointOnFeature(feature);
          [testLng, testLat] = pointOnFeature.geometry.coordinates;
        }
      } catch (e) {
        try {
          // Fallback to pointOnFeature
          const pointOnFeature = turf.pointOnFeature(feature);
          [testLng, testLat] = pointOnFeature.geometry.coordinates;
        } catch (e2) {
          // Ultimate fallback - skip this spawn
          return null;
        }
      }

      // Choose a random Pokemon from the candidates
      const pokeEntry = candidates[Math.floor(Math.random() * candidates.length)];

      // Get spawn level based on player's team average level
      const level = await this.getSpawnLevel();

      // Calculate evolution data with isolated pokedex snapshot
      const evolutionData = await this.calculateEvolutionData(pokeEntry.name, level, pokedexSnapshot);

      // Return all collected data
      return {
        basePokeEntry: pokeEntry,
        level,
        evolutionData,
        spawnLocation: { lat: testLat, lng: testLng },
        landuseData: {
          value: landuseType,
          mapping: mapping,
          feature: feature
        },
        timestamp: Date.now() // For debugging
      };
    } catch (e) {
      logger.error('Error collecting landuse Pokemon data:', e);
      return null;
    }
  }

  /**
   * Create final landuse Pokemon object with pre-calculated data
   * @param {Object} pokemonData - Pre-calculated Pokemon data
   * @returns {Pokemon|null} - Final Pokemon object or null
   */
  createFinalLandusePokemon(pokemonData) {
    try {
      // Create Pokemon with BASE name first, then set evolution data
      const pokemon = new Pokemon(
        pokemonData.basePokeEntry.name,
        pokemonData.basePokeEntry.types[0],
        pokemonData.level
      );

      // Set base properties first
      pokemon.base_name = pokemonData.basePokeEntry.name;
      pokemon.base_sprite = pokemonData.basePokeEntry.image_url;
      pokemon.rarity = pokemonData.basePokeEntry.rarity || 'common';

      // Now set the evolved form data (this is the key fix!)
      pokemon.name = pokemonData.evolutionData.name;
      pokemon.image_url = pokemonData.evolutionData.sprite;
      pokemon.image = pokemonData.evolutionData.sprite; // Ensure image is set
      pokemon.dex_number = pokemonData.evolutionData.dex_number;
      pokemon.types = pokemonData.evolutionData.types;

      // Set evolution chain ID to prevent getDisplayForm from being called again
      if (pokemonData.basePokeEntry.evolution_chain_id) {
        pokemon.evolution_chain_id = pokemonData.basePokeEntry.evolution_chain_id;
      }

      // Set spawn location
      pokemon.spawnLat = pokemonData.spawnLocation.lat;
      pokemon.spawnLng = pokemonData.spawnLocation.lng;
      pokemon.lat = pokemonData.spawnLocation.lat;
      pokemon.lng = pokemonData.spawnLocation.lng;

      // Set landuse-specific properties
      pokemon.landuseSpecial = true;
      pokemon.landuseType = pokemonData.landuseData.value;
      pokemon.landuseTypeName = pokemonData.landuseData.value;
      pokemon.featureId = pokemonData.landuseData.feature.properties.osm_id;

      // Final fallback for image if still not set
      if (!pokemon.image && pokemon.dex_number) {
        pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
      }

      logger.debug(`Created final landuse Pokemon: ${pokemon.name} (base: ${pokemon.base_name}) Level ${pokemon.level}, Landuse: ${pokemon.landuseType}`);

      return pokemon;
    } catch (e) {
      logger.error('Error creating final landuse Pokemon:', e, pokemonData);
      return null;
    }
  }


}
