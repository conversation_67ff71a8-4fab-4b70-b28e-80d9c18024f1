// services/pokemon-spawner.js
// Service for spawning Pokemon in the game world

import { config } from '../config.js';
import { logger } from '../utils/logger.js';
import { gameState } from '../state/game-state.js';
import Pokemon from '../Pokemon.js';
import { getChainIndexForLocation } from '../pokemon-grid.js';
import { getLandusePolygonsGeoJSON, getLanduseForLatLng } from '../overpass-landuse.js';
import { LANDUSE_TYPE_MAPPING } from '../landuse-pokemon-types.js';
import { getSpawnLevel as calculateSpawnLevel } from './spawn-levels.js';
import { updatePokemonFromPokedex } from '../utils/pokemon-utils.js';

/**
 * Calculate distance in meters between two lat/lng points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Distance in meters
 */
export function distanceMeters(lat1, lng1, lat2, lng2) {
  function toRad(x) { return x * Math.PI / 180; }
  const R = 6371000;
  const dLat = toRad(lat2 - lat1);
  const dLng = toRad(lng2 - lng1);
  const a = Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(toRad(lat1)) * Math.cos(toRad(lat2)) *
            Math.sin(dLng/2) * Math.sin(dLng/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}

/**
 * Calculate azimuth (direction) between two points
 * @param {number} lat1 - Latitude of point 1
 * @param {number} lng1 - Longitude of point 1
 * @param {number} lat2 - Latitude of point 2
 * @param {number} lng2 - Longitude of point 2
 * @returns {number} - Azimuth in degrees (0-360)
 */
export function calculateAzimuth(lat1, lng1, lat2, lng2) {
  const toRad = deg => deg * Math.PI / 180;
  const toDeg = rad => rad * 180 / Math.PI;
  const dLon = toRad(lng2 - lng1);
  const y = Math.sin(dLon) * Math.cos(toRad(lat2));
  const x = Math.cos(toRad(lat1)) * Math.sin(toRad(lat2)) - Math.sin(toRad(lat1)) * Math.cos(toRad(lat2)) * Math.cos(dLon);
  let brng = Math.atan2(y, x);
  brng = toDeg(brng);
  return (brng + 360) % 360;
}

/**
 * Generate a random offset in a specific direction
 * @param {number} maxMeters - Maximum distance in meters
 * @param {number} baseLat - Base latitude
 * @param {number} baseLng - Base longitude
 * @param {number} lastLat - Last latitude (for directional spawning)
 * @param {number} lastLng - Last longitude (for directional spawning)
 * @param {number} sectorDeg - Sector angle in degrees
 * @returns {Object} - { lat, lng } offset
 */
function randomOffsetInMetersDirectional(maxMeters, baseLat, baseLng, lastLat, lastLng, sectorDeg = 120) {
  // Determine direction
  let angle = Math.random() * 2 * Math.PI; // Default: all directions allowed
  if (typeof lastLat === 'number' && typeof lastLng === 'number' && (baseLat !== lastLat || baseLng !== lastLng)) {
    // Direction: from last spawn point to current position
    const dx = baseLng - lastLng;
    const dy = baseLat - lastLat;
    const dir = Math.atan2(dy, dx); // Direction in radians
    // Sector angle in radians
    const sector = (sectorDeg / 180) * Math.PI;
    const offset = (Math.random() - 0.5) * sector; // e.g. ±60°
    angle = dir + offset;
  }
  const distance = Math.random() * maxMeters;
  // Convert meters to degrees
  const dLat = Math.cos(angle) * (distance / 111320);
  const dLng = Math.sin(angle) * (distance / (111320 * Math.cos(baseLat * Math.PI / 180)));
  return {
    lat: dLat,
    lng: dLng
  };
}

export class PokemonSpawner {
  constructor() {
    this.spawnRadius = config.pokemon.spawnRadius;
    this.spawnDistanceTrigger = config.pokemon.spawnDistanceTrigger;
    this.spawnBatchSize = config.pokemon.spawnBatchSize;
    this.landuseSpecialBatchSize = config.pokemon.landuseSpecialBatchSize;
    this.maxPokemons = config.pokemon.maxPokemons;

    // Rarity probabilities for Pokémon spawning
    this.rarityProbabilities = {
      common: 0.65,    // 65%
      scarce: 0.22,    // 22%
      rare: 0.08,      // 8%
      legendary: 0.04, // 4%
      mythical: 0.01   // 1%
    };
  }

  /**
   * Recreate Pokemon from stored data
   * @param {Array} storedPokemons - Array of stored Pokemon data
   * @returns {Array} - Array of recreated Pokemon objects
   */
  recreateStoredPokemons(storedPokemons) {
    const recreated = [];

    if (!storedPokemons || !Array.isArray(storedPokemons) || storedPokemons.length === 0) {
      logger.debug('No stored Pokemon to recreate');
      return recreated;
    }

    logger.info(`Recreating ${storedPokemons.length} Pokemon from storage`);

    // Ensure pokedex data is loaded
    const pokedexData = gameState.pokedexData;
    if (!pokedexData || pokedexData.length === 0) {
      logger.warn('Pokedex data not loaded, Pokemon may have missing dex_number values');
    }

    for (const storedPokemon of storedPokemons) {
      try {
        // Use Pokemon.fromJSON to properly recreate the Pokemon with all properties
        const pokemon = Pokemon.fromJSON(storedPokemon);

        // Ensure spawn location properties are set
        if (storedPokemon.spawnLat && storedPokemon.spawnLng) {
          pokemon.spawnLat = storedPokemon.spawnLat;
          pokemon.spawnLng = storedPokemon.spawnLng;
          pokemon.lat = storedPokemon.lat || storedPokemon.spawnLat;
          pokemon.lng = storedPokemon.lng || storedPokemon.spawnLng;
        }

        // Ensure landuse properties are set
        pokemon.landuseSpecial = storedPokemon.landuseSpecial || false;
        pokemon.landuseType = storedPokemon.landuseType || null;
        pokemon.landuseTypeName = storedPokemon.landuseTypeName || null;
        pokemon.featureId = storedPokemon.featureId || null;

        // We don't automatically set landuseSpecial based on landuseType
        // Only Pokemon that were originally spawned as special landuse Pokemon should have landuseSpecial=true

        // Update Pokemon with data from pokedex if needed
        if (!pokemon.dex_number || !pokemon.types || !pokemon.types.length || !pokemon.image) {
          const updated = updatePokemonFromPokedex(pokemon, pokedexData);
          if (updated) {
            logger.debug(`Updated Pokemon ${pokemon.name} with data from pokedex during recreation`);
          }
        }

        // If image is still not set but dex_number is available, set image based on dex_number
        if (!pokemon.image && pokemon.dex_number) {
          pokemon.image = `./src/PokemonSprites/${pokemon.dex_number}.png`;
        }

        // Log successful recreation with dex_number for debugging
        logger.debug(`Recreated Pokemon ${pokemon.name} (ID: ${pokemon.id}) with dex_number: ${pokemon.dex_number}`);

        // Add to game state
        gameState.addPokemon(pokemon);
        recreated.push(pokemon);
      } catch (e) {
        logger.error('Error recreating Pokemon from storage:', e, storedPokemon);
      }
    }

    logger.info(`Successfully recreated ${recreated.length} Pokemon from storage`);
    return recreated;
  }

  /**
   * Create a single Pokemon at a specific location
   * @param {number} lat - Base latitude
   * @param {number} lng - Base longitude
   * @param {boolean} useDirectionalSpawn - Whether to use directional spawning
   * @param {number|null} movementAzimuth - Direction of movement in degrees
   * @returns {Promise<Pokemon|null>} - The created Pokemon or null if creation failed
   */
  async createSinglePokemon(lat, lng, useDirectionalSpawn, movementAzimuth) {
    // Determine spawn point
    let pokeLat, pokeLng;

    try {
      if (useDirectionalSpawn && movementAzimuth !== null) {
        // Directional spawn - use turf.js destination
        // Convert sector angle to radians for random offset
        const sectorAngle = config.ui.debugSectorAngle;
        const randomAngleOffset = ((Math.random() - 0.5) * sectorAngle);
        const distance = Math.random() * this.spawnRadius;

        // Calculate destination point in the direction of movement with random offset
        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          movementAzimuth + randomAngleOffset
        );

        pokeLng = dest.geometry.coordinates[0];
        pokeLat = dest.geometry.coordinates[1];
      } else {
        // Non-directional spawn (360 degrees)
        const randomAngle = Math.random() * 360;
        const distance = Math.random() * this.spawnRadius;

        const dest = turf.destination(
          [lng, lat],
          distance / 1000, // turf uses km
          randomAngle
        );

        pokeLng = dest.geometry.coordinates[0];
        pokeLat = dest.geometry.coordinates[1];
      }
    } catch (e) {
      logger.error('Error using turf.js for Pokemon spawn:', e);
      // Fallback to non-directional if turf fails
      const offset = randomOffsetInMetersDirectional(this.spawnRadius, lat, lng, lat, lng, 360);
      pokeLat = lat + offset.lat;
      pokeLng = lng + offset.lng;
    }

    // Get the base Pokemon for this grid location
    const basePokeEntry = this.getBasePokemonForGrid(pokeLat, pokeLng);
    if (!basePokeEntry) return null;

    const baseName = basePokeEntry.name;
    // Get spawn level based on player's team average level
    const level = await this.getSpawnLevel();
    const rarity = basePokeEntry.rarity || 'common';

    // Create new Pokemon object with the base Pokemon data
    const pokemon = new Pokemon(baseName, basePokeEntry.types[0], level);
    pokemon.base_name = baseName; // explicitly set
    pokemon.base_sprite = basePokeEntry.image_url;
    pokemon.rarity = rarity; // Store the rarity for reference

    // Set types and dex from base Pokemon
    pokemon.types = basePokeEntry.types;
    pokemon.dex_number = basePokeEntry.dex_number;
    pokemon.spawnLat = pokeLat;
    pokemon.spawnLng = pokeLng;
    pokemon.lat = pokeLat;
    pokemon.lng = pokeLng;

    // Log the selected Pokémon with its rarity
    logger.debug(`Selected grid Pokémon: ${baseName} (${basePokeEntry.de || baseName}) - Rarity: ${rarity}`);

    // Determine current form based on level - this will handle evolution
    logger.debug(`Checking evolution for ${pokemon.base_name} (Level ${pokemon.level})`);

    // Get the display form
    const displayForm = await pokemon.getDisplayForm(gameState.pokedexData);

    logger.debug(`Evolution result: ${displayForm.name} (Dex #${displayForm.dex_number})`);

    // Update Pokemon with the evolved form's data if applicable
    pokemon.name = displayForm.name;
    pokemon.image_url = displayForm.sprite;
    pokemon.dex_number = displayForm.dex_number;
    pokemon.types = displayForm.types;

    // Check landuse at spawn location and store it
    try {
      // Get landuse data for this location
      const landuseData = await getLanduseForLatLng(pokemon.lat, pokemon.lng);
      if (landuseData && landuseData.value) {
        // Store both type and value
        pokemon.landuseTypeName = landuseData.value;
        pokemon.landuseType = landuseData.value;

        // For standard spawns, we don't set landuseSpecial to true
        // This ensures they don't get the colored circle
        // pokemon.landuseSpecial = false; // This is the default value

        logger.debug(`Found landuse for regular Pokemon ${pokemon.name}: type=${landuseData.type}, value=${landuseData.value}, special=${pokemon.landuseSpecial || false}`);
      } else {
        logger.debug(`No landuse found for regular Pokemon ${pokemon.name} at ${pokemon.lat},${pokemon.lng}`);
      }
    } catch (e) {
      logger.error('Error getting landuse for regular Pokemon:', e);
    }

    return pokemon;
  }

  /**
   * Spawn random Pokemon around a location
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnRandomPokemons(lat, lng, count = this.spawnBatchSize) {
    const { pokemons } = gameState;

    // Remove oldest if exceeding max
    if (pokemons.length + count > this.maxPokemons) {
      const toRemove = (pokemons.length + count) - this.maxPokemons;
      for (let i = 0; i < toRemove; i++) {
        if (pokemons.length > 0) {
          const removed = pokemons.shift();
          if (removed && gameState.pokemonMarkers.has(removed.id)) {
            gameState.map.removeLayer(gameState.pokemonMarkers.get(removed.id).marker);
            gameState.pokemonMarkers.delete(removed.id);
          }
        }
      }
    }

    // Determine if we should use directional spawning
    let useDirectionalSpawn = false;
    let movementAzimuth = null;

    // Check if we've moved at least 250m from the last spawn point
    if (gameState.lastSpawnLatLng && (lat !== gameState.lastSpawnLatLng.lat || lng !== gameState.lastSpawnLatLng.lng)) {
      try {
        // Calculate distance from last spawn point
        const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

        // Only use directional spawning if we've moved at least 250m
        if (dist >= config.pokemon.spawnDistanceTrigger) {
          // Calculate bearing from last spawn point to current position
          movementAzimuth = turf.bearing(
            [gameState.lastSpawnLatLng.lng, gameState.lastSpawnLatLng.lat],
            [lng, lat]
          );
          // Normalize to 0-360 degrees
          movementAzimuth = (movementAzimuth + 360) % 360;
          useDirectionalSpawn = true;

          logger.debug(`Using directional spawn with azimuth: ${movementAzimuth}° (moved ${dist}m)`);
        }
      } catch (e) {
        logger.error('Error calculating direction for random Pokemon spawn:', e);
        useDirectionalSpawn = false;
      }
    }

    // Create Pokemon in parallel using Promise.all
    logger.debug(`Starting parallel creation of ${count} Pokemon...`);
    const pokemonPromises = [];
    for (let i = 0; i < count; i++) {
      pokemonPromises.push(this.createSinglePokemon(lat, lng, useDirectionalSpawn, movementAzimuth));
    }

    // Wait for all Pokemon to be created
    const createdPokemon = await Promise.all(pokemonPromises);

    // Filter out any null values (failed creations)
    const spawned = createdPokemon.filter(pokemon => pokemon !== null);

    logger.debug(`Successfully created ${spawned.length} Pokemon in parallel`);

    // Add all Pokemon to game state at once
    spawned.forEach(pokemon => {
      gameState.addPokemon(pokemon);
    });

    return spawned;
  }

  /**
   * Get the base Pokemon for a grid cell
   * @param {number} lat - Latitude
   * @param {number} lon - Longitude
   * @returns {Object|null} - The base Pokemon or null if not found
   */
  getBasePokemonForGrid(lat, lon) {
    if (!gameState.chainsList.length) return null;
    const chainIdx = getChainIndexForLocation(lat, lon, gameState.chainsList.length);
    const chainId = gameState.chainsList[chainIdx];

    // Get the base Pokemon for this chain
    const basePokemon = gameState.basePokemonByChain[chainId];
    if (!basePokemon) return null;

    logger.debug(`Found base Pokemon for chain ${chainId}: ${basePokemon.name} (${basePokemon.dex_number})`);
    return basePokemon;
  }

  /**
   * Select a rarity based on the defined probabilities
   * @returns {string} - The selected rarity (common, scarce, rare, legendary, mythical)
   */
  selectRarity() {
    const random = Math.random();
    let cumulativeProbability = 0;

    for (const [rarity, probability] of Object.entries(this.rarityProbabilities)) {
      cumulativeProbability += probability;
      if (random <= cumulativeProbability) {
        return rarity;
      }
    }

    // Fallback to common if something goes wrong
    return 'common';
  }

  /**
   * Get a spawn level for a wild Pokemon based on the player's team
   * Uses the spawn-levels.js module to calculate the level
   * @returns {Promise<number>} - The spawn level (minimum 1)
   */
  async getSpawnLevel() {
    try {
      // Use the imported function from spawn-levels.js
      return await calculateSpawnLevel();
    } catch (e) {
      logger.error('Error determining spawn level:', e);
      // Fallback to a random level between 1-10 if there's an error
      const fallbackLevel = Math.floor(Math.random() * 10) + 1;
      logger.debug(`Using fallback spawn level: ${fallbackLevel}`);
      return fallbackLevel;
    }
  }

  /**
   * Spawn special Pokemon based on land use
   * @param {number} lat - Latitude
   * @param {number} lng - Longitude
   * @param {number} count - Number of Pokemon to spawn
   * @returns {Promise<Array>} - The spawned Pokemon
   */
  async spawnLanduseSpecialPokemons(lat, lng, count = this.landuseSpecialBatchSize) {
    const spawned = [];

    try {
      // Get all landuse/natural/leisure polygons in the area as GeoJSON
      const radius = this.spawnRadius;
      const geojson = await getLandusePolygonsGeoJSON(lat, lng, radius);
      let features = geojson.features.filter(f => f && f.properties && LANDUSE_TYPE_MAPPING[f.properties.value]);
      if (!features.length) return spawned;

      // Determine if we should use directional spawning
      // For initial spawn (when lastSpawnLatLng is null), this will be false,
      // resulting in 360° spawning around the player
      // Only after moving 250m will directional spawning be enabled
      let useDirectionalSpawn = false;
      let movementAzimuth = null;

      // Check if we've moved at least 250m from the last spawn point
      if (gameState.lastSpawnLatLng && (lat !== gameState.lastSpawnLatLng.lat || lng !== gameState.lastSpawnLatLng.lng)) {
        try {
          // Calculate distance from last spawn point
          const dist = distanceMeters(lat, lng, gameState.lastSpawnLatLng.lat, gameState.lastSpawnLatLng.lng);

          // Only use directional spawning if we've moved at least 250m
          if (dist >= config.pokemon.spawnDistanceTrigger) {
            // Calculate bearing from last spawn point to current position
            movementAzimuth = turf.bearing(
              [gameState.lastSpawnLatLng.lng, gameState.lastSpawnLatLng.lat],
              [lng, lat]
            );
            // Normalize to 0-360 degrees
            movementAzimuth = (movementAzimuth + 360) % 360;
            useDirectionalSpawn = true;

            logger.debug(`Using directional spawn for landuse Pokemon with azimuth: ${movementAzimuth}° (moved ${dist}m)`);
          }
        } catch (e) {
          logger.error('Error calculating direction for landuse Pokemon spawn:', e);
          useDirectionalSpawn = false;
        }
      }

      // Filter features based on sector if using directional spawn
      // This only happens after the player has moved at least 250m from the initial spawn
      // For the initial spawn, all features are used (360° around the player)
      let filteredFeatures = features;
      let directionalFeatures = [];

      if (useDirectionalSpawn && movementAzimuth !== null) {
        try {
          // First, get all features in the 60° sector in front of the player
          directionalFeatures = features.filter(f => {
            try {
              // Get center of the feature
              const center = turf.centerOfMass(f).geometry.coordinates;

              // Calculate bearing from current position to feature center
              const polyAzimuth = turf.bearing([lng, lat], [center[0], center[1]]);

              // Normalize to 0-360 degrees
              const normalizedPolyAzimuth = (polyAzimuth + 360) % 360;

              // Calculate the angle difference
              let diff = Math.abs(normalizedPolyAzimuth - movementAzimuth);
              if (diff > 180) diff = 360 - diff;

              // Feature is within 60 degrees of movement direction
              return diff <= 60;
            } catch (e) {
              return false;
            }
          });

          // If we have at least 3 polygons in the direction, use those
          if (directionalFeatures.length >= 3) {
            logger.debug(`Found ${directionalFeatures.length} features in direction sector, using directional spawning`);
            filteredFeatures = directionalFeatures;
          } else if (directionalFeatures.length > 0) {
            // If we have some directional features but less than 3, still use them but with higher priority
            logger.debug(`Only ${directionalFeatures.length} features in direction sector, prioritizing them but including others`);
            // We'll handle the prioritization in the weighting step
            filteredFeatures = features;
          } else {
            // No features in direction, fall back to all features
            logger.debug('No features found in direction sector, using all features');
            filteredFeatures = features;
            // Disable directional spawning since we couldn't find any suitable polygons
            useDirectionalSpawn = false;
          }
        } catch (e) {
          logger.error('Error filtering features by direction:', e);
          filteredFeatures = features;
          useDirectionalSpawn = false;
        }
      }

      // Create a map to track polygons where Pokemon have already spawned
      // We'll use the OSM ID as the key
      const existingSpawnAreas = new Map();

      // Check existing Pokemon to see which landuse areas already have spawns
      gameState.pokemons.forEach(pokemon => {
        if (pokemon.landuseSpecial && pokemon.landuseType) {
          // Find the feature this Pokemon is in
          for (const feature of features) {
            if (feature.properties.value === pokemon.landuseType) {
              try {
                const point = turf.point([pokemon.lng, pokemon.lat]);
                if (turf.booleanPointInPolygon(point, feature)) {
                  // This Pokemon is in this feature
                  const osmId = feature.properties.osm_id;
                  if (osmId) {
                    if (!existingSpawnAreas.has(osmId)) {
                      existingSpawnAreas.set(osmId, 1);
                    } else {
                      existingSpawnAreas.set(osmId, existingSpawnAreas.get(osmId) + 1);
                    }
                  }
                  break;
                }
              } catch (e) {
                // Ignore errors in point-in-polygon check
              }
            }
          }
        }
      });

      // Calculate weights for feature selection
      let normWeights;
      try {
        // Start with area-based weights - smaller areas get slightly higher weight
        const alpha = 0.2; // Weighting factor for area
        const areas = filteredFeatures.map(f => turf.area(f));
        let weights = areas.map(a => Math.pow(1 / (a || 1), alpha));

        // Apply directional bonus if we're using directional spawning
        if (useDirectionalSpawn && directionalFeatures.length > 0) {
          // Create a set of directional feature IDs for quick lookup
          const directionalFeatureIds = new Set(
            directionalFeatures.map(f => f.properties.osm_id)
          );

          // Apply a strong directional bonus (5x) to features in the movement direction
          weights = weights.map((w, i) => {
            const feature = filteredFeatures[i];
            const featureId = feature.properties.osm_id;

            // Check if this feature is in our directional set
            if (directionalFeatureIds.has(featureId)) {
              return w * 5; // 5x bonus for features in direction
            }
            return w;
          });
        }

        // Apply penalty for areas that already have Pokemon
        weights = weights.map((w, i) => {
          const feature = filteredFeatures[i];
          const osmId = feature.properties.osm_id;

          if (existingSpawnAreas.has(osmId)) {
            // Apply a penalty based on how many Pokemon are already in this area
            const count = existingSpawnAreas.get(osmId);
            // Exponential penalty: more Pokemon = much lower chance of spawning more
            return w * Math.pow(0.3, count); // 0.3^1 = 0.3, 0.3^2 = 0.09, etc.
          }
          return w;
        });

        // Normalize weights
        const sum = weights.reduce((a, b) => a + b, 0);
        normWeights = weights.map(w => w / sum);
      } catch (e) {
        logger.error('Error calculating feature weights:', e);
        // Fallback to equal weights
        normWeights = filteredFeatures.map(() => 1 / filteredFeatures.length);
      }

      // Helper function: Pick a feature by weight
      function pickFeatureByWeight(features, weights) {
        const r = Math.random();
        let acc = 0;
        for (let i = 0; i < features.length; i++) {
          acc += weights[i];
          if (r <= acc) return features[i];
        }
        return features[features.length - 1];
      }

      let spawnedCount = 0;
      let attempts = 0;
      const maxAttempts = count * 20;

      while (spawnedCount < count && attempts < maxAttempts) {
        attempts++;

        // Pick a polygon by weight
        const feature = pickFeatureByWeight(filteredFeatures, normWeights);
        if (!feature) continue;

        const landuseType = feature.properties.value;
        const mapping = LANDUSE_TYPE_MAPPING[landuseType];
        if (!mapping || !mapping.type) continue;

        // Get all Pokémon of this type from the pokedex
        const allTypeMatches = (window.pokedexData || gameState.pokedexData).filter(
          p => p.types && p.types.includes(mapping.type)
        );
        if (!allTypeMatches || allTypeMatches.length === 0) continue;

        // Select a rarity based on the defined probabilities
        const targetRarity = this.selectRarity();
        logger.debug(`Selected rarity for landuse spawn: ${targetRarity}`);

        // Try to find Pokémon with the selected rarity and matching type
        let candidates = allTypeMatches.filter(p => p.rarity === targetRarity);

        // If no Pokémon with the selected rarity is available, try other rarities
        if (!candidates || candidates.length === 0) {
          logger.debug(`No ${targetRarity} Pokémon found for type ${mapping.type}, trying fallback rarities`);

          // Define fallback order based on the selected rarity
          let fallbackRarities;
          switch (targetRarity) {
            case 'mythical':
              fallbackRarities = ['legendary', 'rare', 'scarce', 'common'];
              break;
            case 'legendary':
              fallbackRarities = ['rare', 'scarce', 'common', 'mythical'];
              break;
            case 'rare':
              fallbackRarities = ['scarce', 'common', 'legendary', 'mythical'];
              break;
            case 'scarce':
              fallbackRarities = ['common', 'rare', 'legendary', 'mythical'];
              break;
            case 'common':
            default:
              fallbackRarities = ['scarce', 'rare', 'legendary', 'mythical'];
              break;
          }

          // Try each fallback rarity in order
          for (const fallbackRarity of fallbackRarities) {
            candidates = allTypeMatches.filter(p => p.rarity === fallbackRarity);
            if (candidates && candidates.length > 0) {
              logger.debug(`Using fallback rarity ${fallbackRarity} for landuse spawn`);
              break;
            }
          }

          // If still no candidates, use all Pokémon of this type regardless of rarity
          if (!candidates || candidates.length === 0) {
            logger.debug(`No Pokémon found with any rarity for type ${mapping.type}, using all type matches`);
            candidates = allTypeMatches;
          }
        }

        if (!candidates || candidates.length === 0) continue;

        // Generate a random point in the polygon
        let testLng, testLat;

        try {
          // First try to generate a random point within the polygon
          const spawnPoint = turf.randomPoint(1, {bbox: turf.bbox(feature)}).features[0];

          // Check if point is actually in the polygon, otherwise use a point on the feature
          if (turf.booleanPointInPolygon(spawnPoint, feature)) {
            [testLng, testLat] = spawnPoint.geometry.coordinates;
          } else {
            // If random point is outside polygon, use a point on the feature
            const pointOnFeature = turf.pointOnFeature(feature);
            [testLng, testLat] = pointOnFeature.geometry.coordinates;
          }
        } catch (e) {
          logger.error('Error generating random point in polygon:', e);
          try {
            // Fallback to pointOnFeature
            const pointOnFeature = turf.pointOnFeature(feature);
            [testLng, testLat] = pointOnFeature.geometry.coordinates;
          } catch (e2) {
            // Ultimate fallback - use the current location
            logger.error('Ultimate fallback for spawn point:', e2);
            [testLng, testLat] = [lng, lat];
          }
        }

        // Choose a random Pokemon from the candidates
        const pokeEntry = candidates[Math.floor(Math.random() * candidates.length)];
        const baseName = pokeEntry.name;
        // Get spawn level based on player's team average level
        const level = await this.getSpawnLevel();
        const rarity = pokeEntry.rarity || 'common';

        // Log the selected Pokémon with its rarity
        logger.debug(`Selected landuse Pokémon: ${baseName} (${pokeEntry.de || baseName}) - Rarity: ${rarity}, Type: ${mapping.type}`);

        // Create new Pokemon object
        const pokemon = new Pokemon(baseName, pokeEntry.types[0], level);
        pokemon.base_name = baseName; // explicitly set
        pokemon.base_sprite = pokeEntry.image_url;
        pokemon.rarity = rarity; // Store the rarity for reference

        // Set types and dex
        pokemon.types = pokeEntry.types;
        pokemon.dex_number = pokeEntry.dex_number;
        pokemon.spawnLat = testLat;
        pokemon.spawnLng = testLng;
        pokemon.lat = testLat;
        pokemon.lng = testLng;

        // Determine current form based on level - this will handle evolution
        logger.debug(`Checking landuse evolution for ${pokemon.base_name} (Level ${pokemon.level})`);

        // Use await to get the display form
        const displayForm = await pokemon.getDisplayForm(gameState.pokedexData);

        logger.debug(`Landuse evolution result: ${displayForm.name} (Dex #${displayForm.dex_number})`);

        // Update Pokemon with the evolved form's data if applicable
        pokemon.name = displayForm.name;
        pokemon.image_url = displayForm.sprite;
        pokemon.dex_number = displayForm.dex_number;
        pokemon.types = displayForm.types;
        pokemon.landuseSpecial = true;
        pokemon.landuseType = landuseType;
        pokemon.featureId = feature.properties.osm_id; // Store the feature ID for future reference

        // Store the landuse type name for display in tooltip
        if (mapping && mapping.type) {
          pokemon.landuseTypeName = landuseType;
          logger.debug(`Set landuseTypeName for ${pokemon.name} to ${landuseType}`);
        }

        // Update our tracking of spawn areas
        const osmId = feature.properties.osm_id;
        if (osmId) {
          if (!existingSpawnAreas.has(osmId)) {
            existingSpawnAreas.set(osmId, 1);
          } else {
            existingSpawnAreas.set(osmId, existingSpawnAreas.get(osmId) + 1);
          }
        }

        spawned.push(pokemon);
        gameState.addPokemon(pokemon);
        spawnedCount++;
      }

      return spawned;
    } catch (e) {
      logger.error('Error spawning landuse special Pokemon:', e);
      return spawned;
    }
  }
}
